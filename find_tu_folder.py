import os
import glob

def search_tu_folders():
    """搜索所有可能的tu文件夹和图片文件夹"""
    
    # 搜索路径列表
    search_paths = [
        "C:/",
        "D:/", 
        "E:/",
        "F:/",
        os.path.expanduser("~/Desktop"),
        os.path.expanduser("~/Documents"),
        os.path.expanduser("~/Pictures"),
        "C:/Users/<USER>/Desktop",
        "C:/Users/<USER>/Documents", 
        "C:/Users/<USER>/Pictures"
    ]
    
    print("🔍 搜索所有驱动器和常用目录中的'tu'文件夹...")
    
    found_folders = []
    
    for base_path in search_paths:
        if not os.path.exists(base_path):
            continue
            
        print(f"搜索: {base_path}")
        
        try:
            # 搜索tu文件夹
            for root, dirs, files in os.walk(base_path):
                # 限制搜索深度，避免搜索太久
                depth = root.replace(base_path, '').count(os.sep)
                if depth > 3:
                    continue
                    
                for dir_name in dirs:
                    if dir_name.lower() == 'tu':
                        tu_path = os.path.join(root, dir_name)
                        
                        # 统计图片数量
                        image_count = 0
                        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', 
                                          '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
                        
                        for ext in image_extensions:
                            image_count += len(glob.glob(os.path.join(tu_path, ext)))
                        
                        if image_count > 0:
                            found_folders.append((tu_path, image_count))
                            print(f"✅ 找到: {tu_path} ({image_count} 张图片)")
                            
        except PermissionError:
            continue
        except Exception as e:
            print(f"搜索 {base_path} 时出错: {e}")
    
    return found_folders

def search_large_image_folders():
    """搜索包含大量图片的文件夹"""
    
    print("\n🔍 搜索包含大量图片的文件夹...")
    
    search_paths = [
        os.path.expanduser("~/Desktop"),
        "C:/Users/<USER>/Desktop",
        "E:/8/3"
    ]
    
    large_folders = []
    
    for base_path in search_paths:
        if not os.path.exists(base_path):
            continue
            
        try:
            for item in os.listdir(base_path):
                item_path = os.path.join(base_path, item)
                if os.path.isdir(item_path):
                    # 统计图片数量
                    image_count = 0
                    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', 
                                      '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
                    
                    for ext in image_extensions:
                        image_count += len(glob.glob(os.path.join(item_path, ext)))
                    
                    if image_count > 100:  # 超过100张图片
                        large_folders.append((item_path, image_count))
                        print(f"📁 {item}: {image_count} 张图片")
                        
        except Exception as e:
            print(f"搜索 {base_path} 时出错: {e}")
    
    return large_folders

def main():
    print("=" * 60)
    print("🔍 图片文件夹搜索工具")
    print("=" * 60)
    
    # 搜索tu文件夹
    tu_folders = search_tu_folders()
    
    # 搜索大型图片文件夹
    large_folders = search_large_image_folders()
    
    print("\n" + "=" * 60)
    print("📊 搜索结果汇总")
    print("=" * 60)
    
    if tu_folders:
        print("\n🎯 找到的'tu'文件夹:")
        for folder, count in tu_folders:
            print(f"  📁 {folder} ({count} 张图片)")
    else:
        print("\n❌ 未找到名为'tu'的文件夹")
    
    if large_folders:
        print("\n📦 包含大量图片的文件夹:")
        for folder, count in large_folders:
            print(f"  📁 {folder} ({count} 张图片)")
    
    # 推荐使用的文件夹
    all_folders = tu_folders + large_folders
    if all_folders:
        # 按图片数量排序
        all_folders.sort(key=lambda x: x[1], reverse=True)
        best_folder = all_folders[0]
        
        print(f"\n🏆 推荐使用: {best_folder[0]}")
        print(f"   图片数量: {best_folder[1]} 张")
        
        # 生成使用说明
        print(f"\n📝 使用方法:")
        print(f"1. 确认文件夹路径: {best_folder[0]}")
        print(f"2. 修改上传脚本中的folder_path变量")
        print(f"3. 运行批量上传脚本")
    else:
        print("\n❌ 未找到包含大量图片的文件夹")
        print("请检查以下可能的位置:")
        print("- 桌面上是否有图片文件夹")
        print("- 文件夹名称是否正确")
        print("- 图片文件是否存在")

if __name__ == "__main__":
    main()
