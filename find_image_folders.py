import os
import glob

def find_image_folders():
    """搜索包含大量图片的文件夹"""
    
    # 常见的搜索路径
    search_paths = [
        os.path.expanduser("~/Desktop"),
        os.path.expanduser("~/Desktop/tu"),
        os.path.expanduser("~/Documents"),
        "C:/Users/<USER>/Desktop",
        "C:/Users/<USER>/Desktop/tu",
        "E:/8/3/tu",
        "E:/tu",
        "D:/tu"
    ]
    
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
    
    print("搜索包含图片的文件夹...")
    
    for path in search_paths:
        if os.path.exists(path):
            print(f"\n检查路径: {path}")
            try:
                # 统计图片数量
                image_count = 0
                for ext in image_extensions:
                    image_count += len(glob.glob(os.path.join(path, ext)))
                
                if image_count > 0:
                    print(f"  找到 {image_count} 张图片")
                    
                    # 如果图片数量超过100张，显示详细信息
                    if image_count > 100:
                        print(f"  *** 发现大量图片文件夹: {path} ({image_count} 张图片) ***")
                        
                        # 显示前几个文件名
                        sample_files = []
                        for ext in image_extensions:
                            sample_files.extend(glob.glob(os.path.join(path, ext))[:3])
                        
                        print("  示例文件:")
                        for file in sample_files[:5]:
                            print(f"    {os.path.basename(file)}")
                            
            except Exception as e:
                print(f"  错误: {e}")
        else:
            print(f"路径不存在: {path}")
    
    # 搜索桌面上的所有子文件夹
    desktop_path = os.path.expanduser("~/Desktop")
    if os.path.exists(desktop_path):
        print(f"\n搜索桌面子文件夹: {desktop_path}")
        try:
            for item in os.listdir(desktop_path):
                item_path = os.path.join(desktop_path, item)
                if os.path.isdir(item_path):
                    image_count = 0
                    for ext in image_extensions:
                        image_count += len(glob.glob(os.path.join(item_path, ext)))
                    
                    if image_count > 50:  # 只显示超过50张图片的文件夹
                        print(f"  {item}: {image_count} 张图片")
        except Exception as e:
            print(f"搜索桌面子文件夹时出错: {e}")

if __name__ == "__main__":
    find_image_folders()
