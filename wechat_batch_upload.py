import requests
import os
import glob
import time
import json
from datetime import datetime

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

# tu文件夹路径
TU_FOLDER = r"C:\Users\<USER>\Desktop\项老师AI协议\公众号素材\tu"

def get_access_token():
    """获取access_token"""
    url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={APPID}&secret={APPSECRET}"
    
    try:
        response = requests.get(url, timeout=30)
        data = response.json()
        
        if "access_token" in data:
            print(f"✅ 获取access_token成功: {data['access_token'][:20]}...")
            print(f"⏰ 有效期: {data['expires_in']} 秒")
            return data["access_token"]
        else:
            print(f"❌ 获取access_token失败: {data}")
            return None
            
    except Exception as e:
        print(f"❌ 获取access_token异常: {e}")
        return None

def upload_image(image_path, access_token):
    """上传单张图片获取永久URL"""
    url = f"https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image"
    
    try:
        with open(image_path, 'rb') as f:
            files = {"media": f}
            response = requests.post(url, files=files, timeout=60)
            data = response.json()
            
            if "url" in data:
                return True, data["url"], data.get("media_id", "")
            else:
                return False, str(data), ""
                
    except Exception as e:
        return False, str(e), ""

def get_all_images():
    """获取tu文件夹中的所有图片"""
    if not os.path.exists(TU_FOLDER):
        print(f"❌ tu文件夹不存在: {TU_FOLDER}")
        return []
    
    image_files = []
    extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', 
                  '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
    
    for ext in extensions:
        image_files.extend(glob.glob(os.path.join(TU_FOLDER, ext)))
    
    # 去重并排序
    unique_files = sorted(list(set(image_files)))
    return unique_files

def main():
    print("🚀 微信公众号tu文件夹批量上传工具")
    print("=" * 60)
    print(f"📁 文件夹: {TU_FOLDER}")
    
    # 1. 获取所有图片文件
    image_files = get_all_images()
    print(f"📊 图片总数: {len(image_files)} 张")
    
    if not image_files:
        print("❌ 未找到图片文件")
        return
    
    # 2. 获取access_token
    print(f"\n🔑 获取access_token...")
    access_token = get_access_token()
    if not access_token:
        print("❌ 无法获取access_token，请检查AppID和AppSecret")
        return
    
    # 3. 开始批量上传
    print(f"\n📤 开始批量上传 {len(image_files)} 张图片...")
    print(f"⏱️  预计用时: {len(image_files) * 2 / 60:.1f} 分钟")
    
    urls = []
    media_ids = []
    success_count = 0
    failed_count = 0
    start_time = datetime.now()
    
    # 创建详细日志
    log_filename = f"upload_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"微信公众号tu文件夹批量上传日志\n")
        log_file.write(f"开始时间: {start_time}\n")
        log_file.write(f"AppID: {APPID}\n")
        log_file.write(f"文件夹: {TU_FOLDER}\n")
        log_file.write(f"图片总数: {len(image_files)}\n")
        log_file.write("-" * 80 + "\n\n")
        
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            
            # 检查文件大小（微信限制10MB）
            try:
                file_size = os.path.getsize(image_path)
                if file_size > 10 * 1024 * 1024:
                    print(f"⚠️  [{i:4d}/{len(image_files)}] 跳过大文件: {filename} ({file_size/1024/1024:.1f}MB)")
                    failed_count += 1
                    log_file.write(f"SKIP: {filename} - 文件过大 ({file_size/1024/1024:.1f}MB)\n")
                    continue
                    
                if file_size == 0:
                    print(f"⚠️  [{i:4d}/{len(image_files)}] 跳过空文件: {filename}")
                    failed_count += 1
                    log_file.write(f"SKIP: {filename} - 空文件\n")
                    continue
                    
            except Exception as e:
                print(f"❌ [{i:4d}/{len(image_files)}] 无法读取文件: {filename}")
                failed_count += 1
                log_file.write(f"ERROR: {filename} - 无法读取: {e}\n")
                continue
            
            # 上传图片
            print(f"📤 [{i:4d}/{len(image_files)}] 上传: {filename}")
            
            success, result, media_id = upload_image(image_path, access_token)
            
            if success:
                urls.append(result)
                if media_id:
                    media_ids.append(media_id)
                success_count += 1
                print(f"✅ 成功: {result}")
                log_file.write(f"SUCCESS: {filename} -> {result}\n")
                if media_id:
                    log_file.write(f"         media_id: {media_id}\n")
            else:
                failed_count += 1
                print(f"❌ 失败: {result}")
                log_file.write(f"FAIL: {filename} -> {result}\n")
            
            # 控制上传速度，避免请求过快
            time.sleep(1.5)
            
            # 每25张显示详细进度
            if i % 25 == 0:
                elapsed = datetime.now() - start_time
                avg_time_per_image = elapsed.total_seconds() / i
                remaining_images = len(image_files) - i
                estimated_remaining = remaining_images * avg_time_per_image
                
                print(f"\n📊 进度报告:")
                print(f"   完成: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%)")
                print(f"   成功: {success_count} | 失败: {failed_count}")
                print(f"   已用时: {elapsed}")
                print(f"   预计剩余: {estimated_remaining/60:.1f} 分钟")
                print(f"   成功率: {success_count/i*100:.1f}%")
                print("-" * 50)
                
                # 实时保存URL（防止中断丢失数据）
                if urls:
                    with open("image_urls_temp.txt", "w", encoding="utf-8") as temp_f:
                        for url in urls:
                            temp_f.write(url + "\n")
        
        # 写入最终统计
        end_time = datetime.now()
        total_time = end_time - start_time
        
        log_file.write(f"\n" + "=" * 80 + "\n")
        log_file.write(f"上传完成时间: {end_time}\n")
        log_file.write(f"总用时: {total_time}\n")
        log_file.write(f"成功上传: {success_count} 张\n")
        log_file.write(f"失败数量: {failed_count} 张\n")
        log_file.write(f"成功率: {success_count/len(image_files)*100:.2f}%\n")
    
    # 保存最终结果
    if urls:
        # 保存所有URL到文件（每行一个）
        with open("image_urls.txt", "w", encoding="utf-8") as f:
            for url in urls:
                f.write(url + "\n")
        
        # 保存详细信息到JSON文件
        result_data = {
            "upload_time": start_time.isoformat(),
            "total_images": len(image_files),
            "success_count": success_count,
            "failed_count": failed_count,
            "success_rate": f"{success_count/len(image_files)*100:.2f}%",
            "total_time": str(total_time),
            "urls": urls,
            "media_ids": media_ids
        }
        
        with open("upload_result.json", "w", encoding="utf-8") as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎉 批量上传完成!")
        print(f"📊 统计信息:")
        print(f"   总图片数: {len(image_files)} 张")
        print(f"   成功上传: {success_count} 张")
        print(f"   失败数量: {failed_count} 张")
        print(f"   成功率: {success_count/len(image_files)*100:.2f}%")
        print(f"   总用时: {total_time}")
        
        print(f"\n📄 生成的文件:")
        print(f"   image_urls.txt - 所有图片URL ({len(urls)} 个)")
        print(f"   upload_result.json - 详细结果数据")
        print(f"   {log_filename} - 完整上传日志")
        
        # 删除临时文件
        if os.path.exists("image_urls_temp.txt"):
            os.remove("image_urls_temp.txt")
            
        print(f"\n✅ 所有 {len(urls)} 个图片URL已保存到 image_urls.txt 文件中，每行一个！")
        
    else:
        print(f"\n❌ 没有成功上传任何图片，请检查网络连接和配置")

if __name__ == "__main__":
    main()
