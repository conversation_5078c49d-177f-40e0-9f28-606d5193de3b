import os
import glob
import re

def get_image_files():
    """获取所有图片文件"""
    folder_path = "C:/Users/<USER>/Desktop/图片案例"
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
        image_files.extend(glob.glob(os.path.join(folder_path, ext)))
    
    return image_files

def generate_upload_guide():
    """生成手动上传指南"""
    
    image_files = get_image_files()
    
    guide_content = f"""
# 微信公众号图片批量上传指南

## 📊 统计信息
- 图片文件夹: C:/Users/<USER>/Desktop/图片案例
- 图片总数: {len(image_files)} 张
- 支持格式: jpg, jpeg, png, gif, bmp

## 🔧 方法1: 手动上传获取URL规律

### 步骤1: 手动上传测试图片
1. 登录微信公众号后台
2. 进入 "素材管理" -> "图片"
3. 上传前3张图片:
   - {os.path.basename(image_files[0]) if image_files else "无图片"}
   - {os.path.basename(image_files[1]) if len(image_files) > 1 else "无图片"}
   - {os.path.basename(image_files[2]) if len(image_files) > 2 else "无图片"}

### 步骤2: 获取图片URL
1. 上传成功后，右键点击图片
2. 选择 "复制图片地址" 或 "在新标签页中打开"
3. 获取完整的图片URL

### 步骤3: 分析URL规律
微信图片URL通常格式为:
```
https://mmbiz.qpic.cn/mmbiz_jpg/[账号标识]/[图片标识]/0?wx_fmt=jpeg
```

### 步骤4: 批量生成URL
将获取的URL规律填入下面的模板中

## 🔧 方法2: 使用微信开发者工具

### 步骤1: 配置IP白名单
1. 登录微信公众号后台
2. 进入 "开发" -> "基本配置"
3. 添加当前服务器IP到白名单: *************

### 步骤2: 运行批量上传脚本
```bash
python batch_upload_final.py
```

## 📝 图片文件列表
"""
    
    for i, image_path in enumerate(image_files, 1):
        filename = os.path.basename(image_path)
        file_size = os.path.getsize(image_path) / 1024  # KB
        guide_content += f"{i:3d}. {filename} ({file_size:.1f} KB)\n"
    
    with open("upload_guide.txt", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print(f"✅ 上传指南已生成: upload_guide.txt")

def create_url_generator():
    """创建URL生成器"""
    
    image_files = get_image_files()
    
    generator_content = '''
def generate_urls_from_pattern():
    """根据URL规律批量生成所有图片URL"""
    
    # TODO: 请填入从微信后台获取的URL规律
    # 示例: https://mmbiz.qpic.cn/mmbiz_jpg/XXXXXX/{filename}/0?wx_fmt=jpeg
    base_url_pattern = "https://mmbiz.qpic.cn/mmbiz_jpg/YOUR_ACCOUNT_ID/{filename}/0?wx_fmt=jpeg"
    
    image_files = [
'''
    
    for image_path in image_files:
        filename = os.path.basename(image_path)
        # 处理文件名中的特殊字符
        safe_filename = filename.replace(' ', '%20').replace('&', '%26')
        generator_content += f'        "{safe_filename}",\n'
    
    generator_content += '''    ]
    
    urls = []
    for filename in image_files:
        # 根据文件扩展名确定格式
        if filename.lower().endswith(('.jpg', '.jpeg')):
            url = base_url_pattern.replace('mmbiz_jpg', 'mmbiz_jpg').replace('{filename}', filename.replace('.jpg', '').replace('.jpeg', ''))
        elif filename.lower().endswith('.png'):
            url = base_url_pattern.replace('mmbiz_jpg', 'mmbiz_png').replace('{filename}', filename.replace('.png', ''))
        elif filename.lower().endswith('.gif'):
            url = base_url_pattern.replace('mmbiz_jpg', 'mmbiz_gif').replace('{filename}', filename.replace('.gif', ''))
        else:
            url = base_url_pattern.replace('{filename}', filename)
        
        urls.append(url)
    
    return urls

def save_urls_to_file():
    """保存URL到文件"""
    urls = generate_urls_from_pattern()
    
    with open("image_urls.txt", "w", encoding="utf-8") as f:
        for url in urls:
            f.write(url + "\\n")
    
    print(f"✅ 已生成 {len(urls)} 个图片URL")
    print(f"📄 保存到文件: image_urls.txt")

if __name__ == "__main__":
    save_urls_to_file()
'''
    
    with open("url_generator.py", "w", encoding="utf-8") as f:
        f.write(generator_content)
    
    print(f"✅ URL生成器已创建: url_generator.py")

def create_simple_uploader():
    """创建简化版上传器（用于IP白名单配置后）"""
    
    uploader_content = '''import requests
import os
import glob
import time

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

def upload_all_images():
    """上传所有图片"""
    
    # 获取access_token
    token_url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": APPID,
        "secret": APPSECRET
    }
    
    response = requests.get(token_url, params=params)
    token_data = response.json()
    
    if "access_token" not in token_data:
        print(f"❌ 获取token失败: {token_data}")
        return
    
    access_token = token_data["access_token"]
    print(f"✅ 获取token成功")
    
    # 获取图片文件
    folder_path = "C:/Users/<USER>/Desktop/图片案例"
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']:
        image_files.extend(glob.glob(os.path.join(folder_path, ext)))
    
    print(f"📊 找到 {len(image_files)} 张图片")
    
    # 批量上传
    urls = []
    for i, image_path in enumerate(image_files, 1):
        print(f"[{i}/{len(image_files)}] 上传: {os.path.basename(image_path)}")
        
        upload_url = "https://api.weixin.qq.com/cgi-bin/material/add_material"
        upload_params = {
            "access_token": access_token,
            "type": "image"
        }
        
        with open(image_path, 'rb') as f:
            files = {"media": f}
            response = requests.post(upload_url, params=upload_params, files=files)
            data = response.json()
            
            if "url" in data:
                urls.append(data["url"])
                print(f"✅ 成功: {data['url']}")
            else:
                print(f"❌ 失败: {data}")
        
        time.sleep(1)  # 避免请求过快
    
    # 保存URL
    with open("image_urls.txt", "w", encoding="utf-8") as f:
        for url in urls:
            f.write(url + "\\n")
    
    print(f"\\n🎉 上传完成! 成功 {len(urls)} 张")
    print(f"📄 URL已保存到: image_urls.txt")

if __name__ == "__main__":
    upload_all_images()
'''
    
    with open("simple_uploader.py", "w", encoding="utf-8") as f:
        f.write(uploader_content)
    
    print(f"✅ 简化上传器已创建: simple_uploader.py")

def main():
    print("🚀 微信公众号图片上传助手")
    print("=" * 50)
    
    image_files = get_image_files()
    print(f"📁 图片文件夹: C:/Users/<USER>/Desktop/图片案例")
    print(f"📊 图片数量: {len(image_files)} 张")
    
    # 生成各种辅助文件
    generate_upload_guide()
    create_url_generator()
    create_simple_uploader()
    
    print(f"\n📝 已生成以下文件:")
    print(f"1. upload_guide.txt - 详细上传指南")
    print(f"2. url_generator.py - URL批量生成器")
    print(f"3. simple_uploader.py - 简化上传器")
    
    print(f"\n🎯 推荐操作步骤:")
    print(f"1. 阅读 upload_guide.txt 了解详细步骤")
    print(f"2. 手动上传几张图片获取URL规律")
    print(f"3. 修改 url_generator.py 中的URL模板")
    print(f"4. 运行 python url_generator.py 生成所有URL")
    
    print(f"\n⚠️  如果要使用API批量上传:")
    print(f"1. 在微信公众号后台添加IP白名单: *************")
    print(f"2. 运行 python simple_uploader.py")

if __name__ == "__main__":
    main()
