import os
import requests
import json
import time
import glob
from datetime import datetime

def get_image_files():
    """获取所有图片文件"""
    folder_path = "C:/Users/<USER>/Desktop/图片案例"
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
        image_files.extend(glob.glob(os.path.join(folder_path, ext)))
    
    return sorted(image_files)

def test_wechat_api():
    """测试微信API是否可用"""
    print("🔍 测试微信API连接...")
    
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": "wxf8c888bfec881d88",
        "secret": "30a34b7f0cb3ab92ac2788d4fa17e559"
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        data = response.json()
        
        if "access_token" in data:
            print("✅ 微信API连接成功！")
            return True, data["access_token"]
        else:
            print(f"❌ 微信API连接失败: {data}")
            return False, None
            
    except Exception as e:
        print(f"❌ 网络连接异常: {e}")
        return False, None

def upload_single_image(image_path, access_token):
    """上传单张图片"""
    url = "https://api.weixin.qq.com/cgi-bin/material/add_material"
    params = {
        "access_token": access_token,
        "type": "image"
    }
    
    try:
        with open(image_path, 'rb') as f:
            files = {"media": f}
            response = requests.post(url, params=params, files=files, timeout=60)
            data = response.json()
            
            if "url" in data:
                return True, data["url"]
            else:
                return False, str(data)
                
    except Exception as e:
        return False, str(e)

def batch_upload_all():
    """批量上传所有图片"""
    print("🚀 开始批量上传所有图片...")
    
    # 测试API
    api_available, access_token = test_wechat_api()
    if not api_available:
        print("\n❌ 无法使用微信API，请使用手动上传方案")
        return False
    
    # 获取图片文件
    image_files = get_image_files()
    print(f"📊 找到 {len(image_files)} 张图片")
    
    # 开始上传
    urls = []
    success_count = 0
    failed_files = []
    
    start_time = datetime.now()
    
    for i, image_path in enumerate(image_files, 1):
        filename = os.path.basename(image_path)
        print(f"\n📤 [{i}/{len(image_files)}] 上传: {filename}")
        
        # 检查文件大小
        file_size = os.path.getsize(image_path)
        if file_size > 10 * 1024 * 1024:  # 10MB
            print(f"⚠️  文件过大，跳过: {file_size / 1024 / 1024:.1f}MB")
            failed_files.append((filename, "文件过大"))
            continue
        
        # 上传图片
        success, result = upload_single_image(image_path, access_token)
        
        if success:
            urls.append(result)
            success_count += 1
            print(f"✅ 成功: {result}")
        else:
            failed_files.append((filename, result))
            print(f"❌ 失败: {result}")
        
        # 避免请求过快
        time.sleep(1)
        
        # 每10张显示进度
        if i % 10 == 0:
            elapsed = datetime.now() - start_time
            print(f"📊 进度: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%) - 用时: {elapsed}")
    
    # 保存结果
    if urls:
        # 保存URL到文件
        with open("image_urls.txt", "w", encoding="utf-8") as f:
            for url in urls:
                f.write(url + "\n")
        
        # 保存详细日志
        with open(f"upload_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", "w", encoding="utf-8") as f:
            f.write(f"微信公众号图片批量上传日志\n")
            f.write(f"上传时间: {datetime.now()}\n")
            f.write(f"总图片数: {len(image_files)}\n")
            f.write(f"成功上传: {success_count}\n")
            f.write(f"失败数量: {len(failed_files)}\n")
            f.write("-" * 50 + "\n\n")
            
            f.write("成功上传的图片URL:\n")
            for i, url in enumerate(urls, 1):
                f.write(f"{i:3d}. {url}\n")
            
            if failed_files:
                f.write(f"\n失败的图片:\n")
                for filename, error in failed_files:
                    f.write(f"- {filename}: {error}\n")
        
        print(f"\n🎉 批量上传完成!")
        print(f"📊 成功: {success_count}/{len(image_files)} 张")
        print(f"📄 URL已保存到: image_urls.txt")
        
        return True
    else:
        print(f"\n❌ 没有成功上传任何图片")
        return False

def generate_manual_template():
    """生成手动上传模板"""
    print("📝 生成手动上传模板...")
    
    image_files = get_image_files()
    
    template_content = f"""# 微信公众号图片手动上传模板

## 📊 基本信息
- 图片总数: {len(image_files)} 张
- 文件夹: C:/Users/<USER>/Desktop/图片案例

## 🔧 操作步骤

### 1. 手动上传测试图片
请先上传以下3张图片到微信公众号后台:
1. {os.path.basename(image_files[0]) if image_files else "无"}
2. {os.path.basename(image_files[1]) if len(image_files) > 1 else "无"}
3. {os.path.basename(image_files[2]) if len(image_files) > 2 else "无"}

### 2. 获取URL规律
上传后获取图片URL，通常格式为:
https://mmbiz.qpic.cn/mmbiz_jpg/[账号ID]/[图片ID]/0?wx_fmt=jpeg

### 3. 批量生成URL
将获取的URL规律填入下面的Python代码中:

```python
# URL模板 - 请替换为实际获取的URL规律
base_url = "https://mmbiz.qpic.cn/mmbiz_jpg/YOUR_ACCOUNT_ID"

# 所有图片文件名
image_files = [
"""
    
    for image_path in image_files:
        filename = os.path.basename(image_path)
        template_content += f'    "{filename}",\n'
    
    template_content += """]

# 生成所有URL
urls = []
for filename in image_files:
    # 根据文件扩展名调整URL
    if filename.lower().endswith(('.jpg', '.jpeg')):
        url = f"{base_url}/{filename.replace('.jpg', '').replace('.jpeg', '')}/0?wx_fmt=jpeg"
    elif filename.lower().endswith('.png'):
        url = f"{base_url.replace('mmbiz_jpg', 'mmbiz_png')}/{filename.replace('.png', '')}/0?wx_fmt=png"
    else:
        url = f"{base_url}/{filename}/0?wx_fmt=other"
    
    urls.append(url)

# 保存到文件
with open("image_urls.txt", "w", encoding="utf-8") as f:
    for url in urls:
        f.write(url + "\\n")

print(f"已生成 {len(urls)} 个URL")
```

## 📝 完整图片列表
"""
    
    for i, image_path in enumerate(image_files, 1):
        filename = os.path.basename(image_path)
        file_size = os.path.getsize(image_path) / 1024  # KB
        template_content += f"{i:3d}. {filename} ({file_size:.1f} KB)\n"
    
    with open("manual_upload_template.txt", "w", encoding="utf-8") as f:
        f.write(template_content)
    
    print("✅ 手动上传模板已生成: manual_upload_template.txt")

def main():
    print("🚀 微信公众号图片批量上传工具")
    print("=" * 60)
    
    image_files = get_image_files()
    print(f"📁 图片文件夹: C:/Users/<USER>/Desktop/图片案例")
    print(f"📊 图片数量: {len(image_files)} 张")
    
    print(f"\n🎯 选择上传方式:")
    print(f"1. 自动批量上传 (需要IP白名单)")
    print(f"2. 生成手动上传模板")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        success = batch_upload_all()
        if not success:
            print(f"\n💡 建议使用方案2: 手动上传模板")
            generate_manual_template()
    else:
        generate_manual_template()
    
    print(f"\n✅ 操作完成!")

if __name__ == "__main__":
    main()
