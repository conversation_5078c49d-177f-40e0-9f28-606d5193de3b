import requests
import time

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

def test_access_token():
    """测试微信API访问"""
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": APPID,
        "secret": APPSECRET
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        print(f"📡 API响应: {data}")
        
        if "access_token" in data:
            print(f"✅ 成功获取access_token!")
            print(f"🔑 Token: {data['access_token'][:20]}...")
            print(f"⏰ 有效期: {data.get('expires_in', 0)} 秒")
            return True
        else:
            print(f"❌ 获取access_token失败")
            if data.get('errcode') == 40164:
                print(f"🚫 IP白名单错误，当前IP未在白名单中")
                print(f"💡 请在微信公众号后台添加当前IP到白名单")
            return False
            
    except Exception as e:
        print(f"❌ 网络请求异常: {e}")
        return False

def main():
    print("🔍 测试微信公众号API连接...")
    print("=" * 40)
    
    for attempt in range(5):
        print(f"\n🔄 第 {attempt + 1} 次尝试...")
        
        if test_access_token():
            print(f"\n🎉 API连接成功！可以开始批量上传")
            break
        else:
            if attempt < 4:
                print(f"⏳ 等待30秒后重试...")
                time.sleep(30)
            else:
                print(f"\n❌ 多次尝试失败，请检查:")
                print(f"1. IP白名单是否正确配置")
                print(f"2. AppID和AppSecret是否正确")
                print(f"3. 网络连接是否正常")

if __name__ == "__main__":
    main()
