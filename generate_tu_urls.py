import os
import glob

# tu文件夹路径
TU_FOLDER = r"C:\Users\<USER>\Desktop\项老师AI协议\公众号素材\tu"

def get_all_images():
    """获取tu文件夹中的所有图片"""
    if not os.path.exists(TU_FOLDER):
        print(f"❌ tu文件夹不存在: {TU_FOLDER}")
        return []
    
    image_files = []
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', 
                       '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
    
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(TU_FOLDER, ext)))
    
    # 去重并排序
    unique_files = list(set(image_files))
    return sorted(unique_files)

def generate_url_template():
    """生成URL模板"""
    
    image_files = get_all_images()
    print(f"📊 找到 {len(image_files)} 张图片")
    
    # 生成手动上传指南
    guide_content = f"""# 微信公众号tu文件夹图片URL生成指南

## 📊 基本信息
- 文件夹路径: {TU_FOLDER}
- 图片总数: {len(image_files)} 张

## 🔧 操作步骤

### 步骤1: 手动上传测试图片
请先上传以下几张图片到微信公众号后台:
1. {os.path.basename(image_files[0]) if image_files else "无"}
2. {os.path.basename(image_files[1]) if len(image_files) > 1 else "无"}
3. {os.path.basename(image_files[2]) if len(image_files) > 2 else "无"}

### 步骤2: 获取URL规律
上传后获取图片URL，通常格式为:
https://mmbiz.qpic.cn/mmbiz_jpg/[账号ID]/[图片ID]/0?wx_fmt=jpeg

### 步骤3: 修改下面的Python代码
将获取的URL规律填入base_url变量中

### 步骤4: 运行代码生成所有URL
"""
    
    with open("tu_upload_guide.txt", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ 上传指南已生成: tu_upload_guide.txt")

def generate_url_generator():
    """生成URL生成器代码"""
    
    image_files = get_all_images()
    
    generator_code = f'''import os

# TODO: 请在这里填入从微信公众号后台获取的URL规律
# 示例: 如果您上传一张图片后获得URL为:
# https://mmbiz.qpic.cn/mmbiz_jpg/ABC123DEF456/xyz789/0?wx_fmt=jpeg
# 那么 base_url 应该设置为: "https://mmbiz.qpic.cn/mmbiz_jpg/ABC123DEF456"

base_url = "https://mmbiz.qpic.cn/mmbiz_jpg/YOUR_ACCOUNT_ID_HERE"

# 所有图片文件名
image_files = [
'''
    
    # 添加所有图片文件名
    for image_path in image_files:
        filename = os.path.basename(image_path)
        # 处理文件名中的特殊字符
        safe_filename = filename.replace('\\', '\\\\').replace('"', '\\"')
        generator_code += f'    "{safe_filename}",\n'
    
    generator_code += f''']

def generate_urls():
    """生成所有图片URL"""
    
    if "YOUR_ACCOUNT_ID_HERE" in base_url:
        print("⚠️  请先修改 base_url 变量!")
        print("1. 手动上传1张图片到微信公众号后台")
        print("2. 获取图片的完整URL")
        print("3. 修改此脚本中的 base_url 变量")
        print("4. 重新运行此脚本")
        return
    
    urls = []
    
    for filename in image_files:
        # 处理文件名中的特殊字符
        safe_filename = filename.replace(' ', '%20').replace('&', '%26').replace('=', '%3D')
        
        # 根据文件扩展名生成URL
        if filename.lower().endswith(('.jpg', '.jpeg')):
            # 移除扩展名
            name_without_ext = safe_filename.replace('.jpg', '').replace('.jpeg', '').replace('.JPG', '').replace('.JPEG', '')
            url = f"{{base_url}}/{{name_without_ext}}/0?wx_fmt=jpeg"
        elif filename.lower().endswith('.png'):
            name_without_ext = safe_filename.replace('.png', '').replace('.PNG', '')
            url = f"{{base_url.replace('mmbiz_jpg', 'mmbiz_png')}}/{{name_without_ext}}/0?wx_fmt=png"
        elif filename.lower().endswith('.gif'):
            name_without_ext = safe_filename.replace('.gif', '').replace('.GIF', '')
            url = f"{{base_url.replace('mmbiz_jpg', 'mmbiz_gif')}}/{{name_without_ext}}/0?wx_fmt=gif"
        else:
            # 其他格式
            name_without_ext = os.path.splitext(safe_filename)[0]
            url = f"{{base_url}}/{{name_without_ext}}/0?wx_fmt=other"
        
        urls.append(url)
    
    # 保存到文件
    with open("image_urls.txt", "w", encoding="utf-8") as f:
        for url in urls:
            f.write(url + "\\n")
    
    print(f"✅ 已生成 {{len(urls)}} 个图片URL")
    print(f"📄 保存到文件: image_urls.txt")
    
    # 显示前几个URL作为示例
    print(f"\\n📋 前5个URL示例:")
    for i, url in enumerate(urls[:5], 1):
        print(f"{{i}}. {{url}}")
    
    if len(urls) > 5:
        print(f"... 还有 {{len(urls) - 5}} 个URL")

if __name__ == "__main__":
    print("🚀 生成tu文件夹所有图片URL")
    print("=" * 50)
    print(f"📁 图片总数: {{len(image_files)}} 张")
    generate_urls()
'''
    
    with open("tu_url_generator.py", "w", encoding="utf-8") as f:
        f.write(generator_code)
    
    print("✅ URL生成器已创建: tu_url_generator.py")

def create_file_list():
    """创建完整的文件列表"""
    
    image_files = get_all_images()
    
    with open("tu_file_list.txt", "w", encoding="utf-8") as f:
        f.write(f"tu文件夹完整图片列表\\n")
        f.write(f"文件夹: {TU_FOLDER}\\n")
        f.write(f"图片总数: {len(image_files)} 张\\n")
        f.write("-" * 60 + "\\n\\n")
        
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            try:
                file_size = os.path.getsize(image_path) / 1024  # KB
                f.write(f"{i:4d}. {filename} ({file_size:.1f} KB)\\n")
            except:
                f.write(f"{i:4d}. {filename} (无法读取大小)\\n")
    
    print("✅ 文件列表已创建: tu_file_list.txt")

def main():
    print("🚀 tu文件夹URL生成工具")
    print("=" * 50)
    
    # 检查文件夹
    if not os.path.exists(TU_FOLDER):
        print(f"❌ tu文件夹不存在: {TU_FOLDER}")
        return
    
    image_files = get_all_images()
    print(f"📁 文件夹: {TU_FOLDER}")
    print(f"📊 图片数量: {len(image_files)} 张")
    
    # 生成各种文件
    generate_url_template()
    generate_url_generator()
    create_file_list()
    
    print(f"\\n📝 已生成以下文件:")
    print(f"1. tu_upload_guide.txt - 详细上传指南")
    print(f"2. tu_url_generator.py - URL批量生成器")
    print(f"3. tu_file_list.txt - 完整文件列表")
    
    print(f"\\n🎯 推荐操作步骤:")
    print(f"1. 阅读 tu_upload_guide.txt 了解详细步骤")
    print(f"2. 手动上传几张图片获取URL规律")
    print(f"3. 修改 tu_url_generator.py 中的base_url")
    print(f"4. 运行 python tu_url_generator.py 生成所有URL")
    print(f"5. 获得 image_urls.txt 文件，包含所有{len(image_files)}张图片的URL")

if __name__ == "__main__":
    main()
