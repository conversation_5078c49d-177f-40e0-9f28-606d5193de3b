import os
import requests
import json
import time
import glob
from datetime import datetime

# tu文件夹路径
TU_FOLDER = r"C:\Users\<USER>\Desktop\项老师AI协议\公众号素材\tu"

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

def get_access_token():
    """获取微信access_token"""
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": APPID,
        "secret": APPSECRET
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if "access_token" in data:
            print(f"✅ 获取access_token成功")
            return data["access_token"]
        else:
            print(f"❌ 获取access_token失败: {data}")
            return None
            
    except Exception as e:
        print(f"❌ 获取access_token异常: {e}")
        return None

def upload_image(image_path, access_token):
    """上传单张图片"""
    url = "https://api.weixin.qq.com/cgi-bin/material/add_material"
    params = {
        "access_token": access_token,
        "type": "image"
    }
    
    try:
        with open(image_path, 'rb') as f:
            files = {"media": f}
            response = requests.post(url, params=params, files=files, timeout=60)
            data = response.json()
            
            if "url" in data:
                return True, data["url"]
            else:
                return False, str(data)
                
    except Exception as e:
        return False, str(e)

def get_all_images():
    """获取tu文件夹中的所有图片"""
    if not os.path.exists(TU_FOLDER):
        print(f"❌ tu文件夹不存在: {TU_FOLDER}")
        return []
    
    image_files = []
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', 
                       '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
    
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(TU_FOLDER, ext)))
    
    # 去重并排序
    unique_files = list(set(image_files))
    return sorted(unique_files)

def main():
    print("🚀 开始批量上传tu文件夹中的所有图片...")
    print(f"📁 文件夹路径: {TU_FOLDER}")
    
    # 获取所有图片
    image_files = get_all_images()
    print(f"📊 图片总数: {len(image_files)} 张")
    
    if not image_files:
        print("❌ 未找到图片文件")
        return
    
    # 获取access_token
    print("🔑 获取微信access_token...")
    access_token = get_access_token()
    if not access_token:
        print("❌ 无法获取access_token，请检查网络和配置")
        return
    
    # 开始批量上传
    urls = []
    success_count = 0
    failed_count = 0
    
    start_time = datetime.now()
    
    # 创建日志文件
    log_filename = f"upload_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    print(f"📤 开始上传，日志文件: {log_filename}")
    
    with open(log_filename, 'w', encoding='utf-8') as log_file:
        log_file.write(f"微信公众号tu文件夹批量上传日志\n")
        log_file.write(f"开始时间: {start_time}\n")
        log_file.write(f"文件夹: {TU_FOLDER}\n")
        log_file.write(f"图片总数: {len(image_files)}\n")
        log_file.write("-" * 60 + "\n\n")
        
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            
            # 检查文件大小（微信限制10MB）
            try:
                file_size = os.path.getsize(image_path)
                if file_size > 10 * 1024 * 1024:
                    print(f"⚠️  [{i}/{len(image_files)}] 跳过大文件: {filename} ({file_size / 1024 / 1024:.1f}MB)")
                    failed_count += 1
                    log_file.write(f"SKIP: {filename} - 文件过大 ({file_size / 1024 / 1024:.1f}MB)\n")
                    continue
            except Exception as e:
                print(f"❌ [{i}/{len(image_files)}] 无法读取: {filename}")
                failed_count += 1
                log_file.write(f"ERROR: {filename} - 无法读取文件: {e}\n")
                continue
            
            # 上传图片
            print(f"📤 [{i}/{len(image_files)}] 上传: {filename}")
            success, result = upload_image(image_path, access_token)
            
            if success:
                urls.append(result)
                success_count += 1
                print(f"✅ 成功: {result}")
                log_file.write(f"SUCCESS: {filename} -> {result}\n")
            else:
                failed_count += 1
                print(f"❌ 失败: {result}")
                log_file.write(f"FAIL: {filename} -> {result}\n")
            
            # 避免请求过快
            time.sleep(1)
            
            # 每50张显示进度
            if i % 50 == 0:
                elapsed = datetime.now() - start_time
                avg_time_per_image = elapsed.total_seconds() / i
                remaining_images = len(image_files) - i
                estimated_remaining = remaining_images * avg_time_per_image
                
                print(f"📊 进度: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%)")
                print(f"⏱️  已用时: {elapsed}")
                print(f"🔮 预计剩余: {estimated_remaining/60:.1f}分钟")
                print(f"✅ 成功: {success_count} | ❌ 失败: {failed_count}")
                
                # 实时保存URL（防止中断丢失）
                if urls:
                    with open("image_urls_temp.txt", "w", encoding="utf-8") as temp_f:
                        for url in urls:
                            temp_f.write(url + "\n")
        
        # 写入最终统计
        end_time = datetime.now()
        total_time = end_time - start_time
        
        log_file.write(f"\n" + "-" * 60 + "\n")
        log_file.write(f"完成时间: {end_time}\n")
        log_file.write(f"总用时: {total_time}\n")
        log_file.write(f"成功上传: {success_count} 张\n")
        log_file.write(f"失败数量: {failed_count} 张\n")
        log_file.write(f"成功率: {success_count/len(image_files)*100:.1f}%\n")
    
    # 保存所有成功的URL到最终文件
    if urls:
        with open("image_urls.txt", "w", encoding="utf-8") as f:
            for url in urls:
                f.write(url + "\n")
        
        print(f"\n🎉 批量上传完成!")
        print(f"📊 总图片: {len(image_files)} 张")
        print(f"✅ 成功: {success_count} 张")
        print(f"❌ 失败: {failed_count} 张")
        print(f"📈 成功率: {success_count/len(image_files)*100:.1f}%")
        print(f"⏱️  总用时: {total_time}")
        print(f"📄 URL文件: image_urls.txt")
        print(f"📄 日志文件: {log_filename}")
        
        # 删除临时文件
        if os.path.exists("image_urls_temp.txt"):
            os.remove("image_urls_temp.txt")
    else:
        print(f"\n❌ 没有成功上传任何图片")

if __name__ == "__main__":
    main()
