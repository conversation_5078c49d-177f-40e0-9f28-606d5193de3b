import os
import requests
import json
import time
import glob

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

# 图片文件夹路径
FOLDER_PATH = r"C:/Users/<USER>/Desktop/图片案例"

def get_access_token():
    """获取微信access_token"""
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": APPID,
        "secret": APPSECRET
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if "access_token" in data:
            print(f"✅ 获取access_token成功")
            return data["access_token"]
        else:
            print(f"❌ 获取access_token失败: {data}")
            return None
            
    except Exception as e:
        print(f"❌ 获取access_token异常: {e}")
        return None

def upload_image(image_path, access_token):
    """上传单张图片"""
    url = f"https://api.weixin.qq.com/cgi-bin/material/add_material"
    params = {
        "access_token": access_token,
        "type": "image"
    }
    
    try:
        with open(image_path, 'rb') as f:
            files = {"media": f}
            response = requests.post(url, params=params, files=files, timeout=60)
            data = response.json()
            
            if "url" in data:
                return data["url"]
            else:
                print(f"❌ 上传失败: {os.path.basename(image_path)} -> {data}")
                return None
                
    except Exception as e:
        print(f"❌ 上传异常: {os.path.basename(image_path)} -> {e}")
        return None

def main():
    # 获取所有图片文件
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
        image_files.extend(glob.glob(os.path.join(FOLDER_PATH, ext)))
    
    print(f"📁 图片文件夹: {FOLDER_PATH}")
    print(f"📊 图片数量: {len(image_files)}")
    
    # 获取access_token
    access_token = get_access_token()
    if not access_token:
        print("❌ 无法获取access_token，请检查:")
        print("1. 网络连接")
        print("2. AppID和AppSecret是否正确")
        print("3. IP是否在微信白名单中")
        return
    
    # 开始批量上传
    urls = []
    success_count = 0
    
    print(f"\n📤 开始批量上传...")
    
    for i, image_path in enumerate(image_files, 1):
        print(f"[{i}/{len(image_files)}] 上传: {os.path.basename(image_path)}")
        
        url = upload_image(image_path, access_token)
        if url:
            urls.append(url)
            success_count += 1
            print(f"✅ 成功: {url}")
        
        # 每次上传后等待1秒
        time.sleep(1)
        
        # 每10张显示进度
        if i % 10 == 0:
            print(f"📊 进度: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%)")
    
    # 保存URL到文件
    if urls:
        with open("image_urls.txt", "w", encoding="utf-8") as f:
            for url in urls:
                f.write(url + "\n")
        
        print(f"\n🎉 上传完成!")
        print(f"📊 成功上传: {success_count}/{len(image_files)} 张")
        print(f"📄 URL已保存到: image_urls.txt")
    else:
        print(f"\n❌ 没有成功上传任何图片")

if __name__ == "__main__":
    main()
