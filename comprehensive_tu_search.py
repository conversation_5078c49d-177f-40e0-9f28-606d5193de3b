import os
import glob
import subprocess

def search_tu_folders():
    """全面搜索tu文件夹"""
    
    print("🔍 全面搜索 'tu' 文件夹...")
    
    # 常见搜索路径
    search_paths = [
        "C:/tu",
        "D:/tu", 
        "E:/tu",
        "F:/tu",
        os.path.expanduser("~/Desktop/tu"),
        os.path.expanduser("~/Documents/tu"),
        os.path.expanduser("~/Pictures/tu"),
        "C:/Users/<USER>/Desktop/tu",
        "C:/Users/<USER>/Documents/tu",
        "C:/Users/<USER>/Pictures/tu",
        "E:/8/3/tu"
    ]
    
    found_folders = []
    
    for path in search_paths:
        if os.path.exists(path):
            print(f"✅ 找到: {path}")
            
            # 统计图片数量
            image_count = 0
            image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', 
                              '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
            
            for ext in image_extensions:
                image_count += len(glob.glob(os.path.join(path, ext)))
            
            found_folders.append((path, image_count))
            print(f"   📊 图片数量: {image_count}")
        else:
            print(f"❌ 不存在: {path}")
    
    return found_folders

def search_with_dir_command():
    """使用dir命令搜索"""
    print("\n🔍 使用dir命令搜索tu文件夹...")
    
    drives = ['C:', 'D:', 'E:', 'F:']
    found_folders = []
    
    for drive in drives:
        if os.path.exists(drive + '/'):
            print(f"搜索驱动器: {drive}")
            try:
                # 搜索根目录下的tu文件夹
                tu_path = os.path.join(drive, 'tu')
                if os.path.exists(tu_path):
                    image_count = 0
                    for ext in ['*.jpg', '*.png', '*.gif', '*.bmp']:
                        image_count += len(glob.glob(os.path.join(tu_path, ext)))
                    
                    found_folders.append((tu_path, image_count))
                    print(f"✅ 找到: {tu_path} ({image_count} 张图片)")
                
            except Exception as e:
                print(f"搜索 {drive} 时出错: {e}")
    
    return found_folders

def search_desktop_subfolders():
    """搜索桌面子文件夹中可能的tu文件夹"""
    print("\n🔍 搜索桌面子文件夹...")
    
    desktop_path = os.path.expanduser("~/Desktop")
    found_folders = []
    
    if os.path.exists(desktop_path):
        try:
            for item in os.listdir(desktop_path):
                item_path = os.path.join(desktop_path, item)
                if os.path.isdir(item_path):
                    # 检查是否是tu文件夹或包含大量图片
                    if item.lower() == 'tu' or 'tu' in item.lower():
                        image_count = 0
                        for ext in ['*.jpg', '*.png', '*.gif', '*.bmp']:
                            image_count += len(glob.glob(os.path.join(item_path, ext)))
                        
                        if image_count > 0:
                            found_folders.append((item_path, image_count))
                            print(f"✅ 找到可能的tu文件夹: {item_path} ({image_count} 张图片)")
                    
                    # 也检查包含大量图片的文件夹
                    elif os.path.isdir(item_path):
                        image_count = 0
                        for ext in ['*.jpg', '*.png', '*.gif', '*.bmp']:
                            image_count += len(glob.glob(os.path.join(item_path, ext)))
                        
                        if image_count > 1000:  # 超过1000张图片
                            found_folders.append((item_path, image_count))
                            print(f"🎯 发现大型图片文件夹: {item_path} ({image_count} 张图片)")
                        
        except Exception as e:
            print(f"搜索桌面时出错: {e}")
    
    return found_folders

def manual_input():
    """手动输入tu文件夹路径"""
    print("\n📝 如果您知道tu文件夹的确切位置，请直接输入:")
    
    while True:
        path = input("请输入tu文件夹完整路径 (或按Enter跳过): ").strip().strip('"')
        
        if not path:
            return None
            
        if os.path.exists(path):
            # 检查是否包含图片
            image_count = 0
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']:
                image_count += len(glob.glob(os.path.join(path, ext)))
            
            if image_count > 0:
                print(f"✅ 确认文件夹: {path}")
                print(f"📊 图片数量: {image_count}")
                return (path, image_count)
            else:
                print("❌ 该文件夹中没有图片文件，请重新输入")
        else:
            print("❌ 路径不存在，请重新输入")

def main():
    print("🚀 tu文件夹全面搜索工具")
    print("=" * 50)
    
    all_found_folders = []
    
    # 方法1: 搜索常见位置
    found1 = search_tu_folders()
    all_found_folders.extend(found1)
    
    # 方法2: 使用dir命令搜索
    found2 = search_with_dir_command()
    all_found_folders.extend(found2)
    
    # 方法3: 搜索桌面子文件夹
    found3 = search_desktop_subfolders()
    all_found_folders.extend(found3)
    
    # 去重
    unique_folders = {}
    for folder, count in all_found_folders:
        if folder not in unique_folders:
            unique_folders[folder] = count
    
    print("\n" + "=" * 50)
    print("📊 搜索结果汇总")
    print("=" * 50)
    
    if unique_folders:
        print("\n🎯 找到的图片文件夹:")
        sorted_folders = sorted(unique_folders.items(), key=lambda x: x[1], reverse=True)
        
        for i, (folder, count) in enumerate(sorted_folders, 1):
            print(f"{i}. {folder} ({count} 张图片)")
        
        # 推荐最佳选择
        best_folder, best_count = sorted_folders[0]
        print(f"\n🏆 推荐使用: {best_folder}")
        print(f"   图片数量: {best_count} 张")
        
        return best_folder
    else:
        print("\n❌ 自动搜索未找到tu文件夹")
        
        # 提供手动输入选项
        result = manual_input()
        if result:
            return result[0]
    
    return None

if __name__ == "__main__":
    tu_folder = main()
    
    if tu_folder:
        print(f"\n✅ 找到tu文件夹: {tu_folder}")
        
        # 统计最终的图片数量
        total_images = 0
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
            total_images += len(glob.glob(os.path.join(tu_folder, ext)))
        
        print(f"📊 图片总数: {total_images} 张")
        
        if total_images > 0:
            print(f"\n🎯 现在可以使用此路径进行批量上传!")
            print(f"文件夹路径: {tu_folder}")
        else:
            print(f"\n⚠️  该文件夹中没有图片文件")
    else:
        print(f"\n❌ 未找到tu文件夹，请检查文件夹是否存在")
