import requests
import os
import glob
import time
from datetime import datetime

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

# tu文件夹路径
TU_FOLDER = r"C:\Users\<USER>\Desktop\项老师AI协议\公众号素材\tu"

def test_and_upload():
    """测试API并开始上传"""
    
    print("🚀 开始批量上传tu文件夹图片")
    print("=" * 50)
    
    # 1. 获取access_token
    print("🔑 获取access_token...")
    token_url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": APPID,
        "secret": APPSECRET
    }
    
    try:
        response = requests.get(token_url, params=params, timeout=30)
        data = response.json()
        
        if "access_token" in data:
            access_token = data["access_token"]
            print(f"✅ 获取access_token成功!")
            print(f"🔑 Token: {access_token[:20]}...")
        else:
            print(f"❌ 获取access_token失败: {data}")
            return
            
    except Exception as e:
        print(f"❌ 网络异常: {e}")
        return
    
    # 2. 获取所有图片文件
    print(f"\n📁 扫描图片文件...")
    if not os.path.exists(TU_FOLDER):
        print(f"❌ 文件夹不存在: {TU_FOLDER}")
        return
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
        image_files.extend(glob.glob(os.path.join(TU_FOLDER, ext)))
    
    # 去重并排序
    image_files = sorted(list(set(image_files)))
    print(f"📊 找到图片: {len(image_files)} 张")
    
    if not image_files:
        print("❌ 未找到图片文件")
        return
    
    # 3. 开始批量上传
    print(f"\n📤 开始批量上传...")
    
    urls = []
    success_count = 0
    failed_count = 0
    start_time = datetime.now()
    
    # 创建日志文件
    log_file = f"upload_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(log_file, 'w', encoding='utf-8') as log:
        log.write(f"微信公众号批量上传日志\n")
        log.write(f"开始时间: {start_time}\n")
        log.write(f"AppID: {APPID}\n")
        log.write(f"文件夹: {TU_FOLDER}\n")
        log.write(f"图片总数: {len(image_files)}\n")
        log.write("-" * 60 + "\n\n")
        
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            
            # 检查文件大小
            try:
                file_size = os.path.getsize(image_path)
                if file_size > 10 * 1024 * 1024:  # 10MB限制
                    print(f"⚠️  [{i}/{len(image_files)}] 跳过大文件: {filename}")
                    failed_count += 1
                    log.write(f"SKIP: {filename} - 文件过大\n")
                    continue
            except:
                print(f"❌ [{i}/{len(image_files)}] 无法读取: {filename}")
                failed_count += 1
                log.write(f"ERROR: {filename} - 无法读取\n")
                continue
            
            # 上传图片
            print(f"📤 [{i}/{len(image_files)}] {filename}")
            
            upload_url = "https://api.weixin.qq.com/cgi-bin/material/add_material"
            upload_params = {
                "access_token": access_token,
                "type": "image"
            }
            
            try:
                with open(image_path, 'rb') as f:
                    files = {"media": f}
                    upload_response = requests.post(upload_url, params=upload_params, files=files, timeout=60)
                    upload_data = upload_response.json()
                    
                    if "url" in upload_data:
                        url = upload_data["url"]
                        urls.append(url)
                        success_count += 1
                        print(f"✅ 成功: {url}")
                        log.write(f"SUCCESS: {filename} -> {url}\n")
                    else:
                        failed_count += 1
                        print(f"❌ 失败: {upload_data}")
                        log.write(f"FAIL: {filename} -> {upload_data}\n")
                        
            except Exception as e:
                failed_count += 1
                print(f"❌ 异常: {e}")
                log.write(f"ERROR: {filename} -> {e}\n")
            
            # 控制上传速度
            time.sleep(1)
            
            # 每50张显示进度
            if i % 50 == 0:
                elapsed = datetime.now() - start_time
                avg_time = elapsed.total_seconds() / i
                remaining = (len(image_files) - i) * avg_time
                
                print(f"📊 进度: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%)")
                print(f"⏱️  用时: {elapsed} | 预计剩余: {remaining/60:.1f}分钟")
                print(f"✅ 成功: {success_count} | ❌ 失败: {failed_count}")
                
                # 实时保存URL
                with open("image_urls_temp.txt", "w", encoding="utf-8") as temp_f:
                    for url in urls:
                        temp_f.write(url + "\n")
        
        # 最终统计
        end_time = datetime.now()
        total_time = end_time - start_time
        
        log.write(f"\n完成时间: {end_time}\n")
        log.write(f"总用时: {total_time}\n")
        log.write(f"成功: {success_count}\n")
        log.write(f"失败: {failed_count}\n")
    
    # 保存最终URL文件
    if urls:
        with open("image_urls.txt", "w", encoding="utf-8") as f:
            for url in urls:
                f.write(url + "\n")
        
        print(f"\n🎉 上传完成!")
        print(f"📊 总数: {len(image_files)} | 成功: {success_count} | 失败: {failed_count}")
        print(f"📈 成功率: {success_count/len(image_files)*100:.1f}%")
        print(f"⏱️  总用时: {total_time}")
        print(f"📄 URL文件: image_urls.txt ({len(urls)} 个URL)")
        print(f"📄 日志文件: {log_file}")
        
        # 删除临时文件
        if os.path.exists("image_urls_temp.txt"):
            os.remove("image_urls_temp.txt")
    else:
        print(f"\n❌ 没有成功上传任何图片")

if __name__ == "__main__":
    test_and_upload()
