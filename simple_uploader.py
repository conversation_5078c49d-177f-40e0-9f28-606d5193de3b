import requests
import os
import glob
import time

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

def upload_all_images():
    """上传所有图片"""
    
    # 获取access_token
    token_url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": APPID,
        "secret": APPSECRET
    }
    
    response = requests.get(token_url, params=params)
    token_data = response.json()
    
    if "access_token" not in token_data:
        print(f"❌ 获取token失败: {token_data}")
        return
    
    access_token = token_data["access_token"]
    print(f"✅ 获取token成功")
    
    # 获取图片文件
    folder_path = "C:/Users/<USER>/Desktop/图片案例"
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']:
        image_files.extend(glob.glob(os.path.join(folder_path, ext)))
    
    print(f"📊 找到 {len(image_files)} 张图片")
    
    # 批量上传
    urls = []
    for i, image_path in enumerate(image_files, 1):
        print(f"[{i}/{len(image_files)}] 上传: {os.path.basename(image_path)}")
        
        upload_url = "https://api.weixin.qq.com/cgi-bin/material/add_material"
        upload_params = {
            "access_token": access_token,
            "type": "image"
        }
        
        with open(image_path, 'rb') as f:
            files = {"media": f}
            response = requests.post(upload_url, params=upload_params, files=files)
            data = response.json()
            
            if "url" in data:
                urls.append(data["url"])
                print(f"✅ 成功: {data['url']}")
            else:
                print(f"❌ 失败: {data}")
        
        time.sleep(1)  # 避免请求过快
    
    # 保存URL
    with open("image_urls.txt", "w", encoding="utf-8") as f:
        for url in urls:
            f.write(url + "\n")
    
    print(f"\n🎉 上传完成! 成功 {len(urls)} 张")
    print(f"📄 URL已保存到: image_urls.txt")

if __name__ == "__main__":
    upload_all_images()
