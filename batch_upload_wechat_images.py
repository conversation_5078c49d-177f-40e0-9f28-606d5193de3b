import os
import requests
import json
import time
import glob
from datetime import datetime

class WeChatImageUploader:
    def __init__(self, appid, appsecret):
        self.appid = appid
        self.appsecret = appsecret
        self.access_token = None
        self.token_expires_at = 0
        
    def get_access_token(self):
        """获取微信access_token"""
        current_time = time.time()
        
        # 如果token还没过期，直接返回
        if self.access_token and current_time < self.token_expires_at:
            return self.access_token
            
        url = "https://api.weixin.qq.com/cgi-bin/token"
        params = {
            "grant_type": "client_credential",
            "appid": self.appid,
            "secret": self.appsecret
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            data = response.json()
            
            if "access_token" in data:
                self.access_token = data["access_token"]
                # token有效期7200秒，提前5分钟刷新
                self.token_expires_at = current_time + data.get("expires_in", 7200) - 300
                print(f"✅ 获取access_token成功: {self.access_token[:20]}...")
                return self.access_token
            else:
                print(f"❌ 获取access_token失败: {data}")
                return None
                
        except Exception as e:
            print(f"❌ 获取access_token异常: {e}")
            return None
    
    def upload_image(self, image_path):
        """上传单张图片到微信永久素材"""
        access_token = self.get_access_token()
        if not access_token:
            return None
            
        url = f"https://api.weixin.qq.com/cgi-bin/material/add_material"
        params = {
            "access_token": access_token,
            "type": "image"
        }
        
        try:
            with open(image_path, 'rb') as f:
                files = {"media": f}
                response = requests.post(url, params=params, files=files, timeout=60)
                data = response.json()
                
                if "media_id" in data and "url" in data:
                    print(f"✅ 上传成功: {os.path.basename(image_path)} -> {data['url']}")
                    return data["url"]
                else:
                    print(f"❌ 上传失败: {os.path.basename(image_path)} -> {data}")
                    return None
                    
        except Exception as e:
            print(f"❌ 上传异常: {os.path.basename(image_path)} -> {e}")
            return None
    
    def batch_upload_folder(self, folder_path, output_file="image_urls.txt"):
        """批量上传文件夹中的所有图片"""
        if not os.path.exists(folder_path):
            print(f"❌ 文件夹不存在: {folder_path}")
            return
            
        # 支持的图片格式
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', 
                          '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']
        
        # 获取所有图片文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(folder_path, ext)))
        
        if not image_files:
            print(f"❌ 文件夹中没有找到图片文件: {folder_path}")
            return
            
        print(f"📁 找到 {len(image_files)} 张图片")
        print(f"📤 开始批量上传...")
        
        # 存储所有URL
        urls = []
        success_count = 0
        fail_count = 0
        
        # 创建日志文件
        log_file = f"upload_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(log_file, 'w', encoding='utf-8') as log:
            log.write(f"微信公众号图片批量上传日志\n")
            log.write(f"开始时间: {datetime.now()}\n")
            log.write(f"文件夹: {folder_path}\n")
            log.write(f"图片总数: {len(image_files)}\n")
            log.write("-" * 50 + "\n\n")
            
            for i, image_path in enumerate(image_files, 1):
                print(f"\n📤 [{i}/{len(image_files)}] 上传: {os.path.basename(image_path)}")
                
                # 检查文件大小（微信限制10MB）
                file_size = os.path.getsize(image_path)
                if file_size > 10 * 1024 * 1024:
                    print(f"⚠️  文件过大，跳过: {file_size / 1024 / 1024:.1f}MB")
                    log.write(f"SKIP: {os.path.basename(image_path)} - 文件过大 ({file_size / 1024 / 1024:.1f}MB)\n")
                    fail_count += 1
                    continue
                
                url = self.upload_image(image_path)
                
                if url:
                    urls.append(url)
                    success_count += 1
                    log.write(f"SUCCESS: {os.path.basename(image_path)} -> {url}\n")
                else:
                    fail_count += 1
                    log.write(f"FAIL: {os.path.basename(image_path)}\n")
                
                # 避免请求过于频繁，每次上传后等待1秒
                time.sleep(1)
                
                # 每上传50张图片显示进度
                if i % 50 == 0:
                    print(f"📊 进度: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%)")
            
            log.write(f"\n" + "-" * 50 + "\n")
            log.write(f"上传完成时间: {datetime.now()}\n")
            log.write(f"成功: {success_count} 张\n")
            log.write(f"失败: {fail_count} 张\n")
        
        # 保存所有URL到文件
        if urls:
            with open(output_file, 'w', encoding='utf-8') as f:
                for url in urls:
                    f.write(url + '\n')
            
            print(f"\n🎉 批量上传完成!")
            print(f"📊 成功: {success_count} 张")
            print(f"📊 失败: {fail_count} 张")
            print(f"📄 URL文件: {output_file}")
            print(f"📄 日志文件: {log_file}")
        else:
            print(f"\n❌ 没有成功上传任何图片")

def main():
    # 微信公众号配置
    APPID = "wxf8c888bfec881d88"
    APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"
    
    # 创建上传器
    uploader = WeChatImageUploader(APPID, APPSECRET)
    
    # 请用户指定图片文件夹路径
    print("🔍 请指定图片文件夹路径:")
    print("1. 桌面/图片案例")
    print("2. 自定义路径")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        folder_path = os.path.expanduser("~/Desktop/图片案例")
    else:
        folder_path = input("请输入图片文件夹完整路径: ").strip()
    
    # 开始批量上传
    uploader.batch_upload_folder(folder_path)

if __name__ == "__main__":
    main()
