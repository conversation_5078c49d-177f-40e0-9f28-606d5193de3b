import os
import glob

def generate_image_urls():
    """快速生成图片URL"""
    
    # 获取所有图片文件
    folder_path = "C:/Users/<USER>/Desktop/图片案例"
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
        image_files.extend(glob.glob(os.path.join(folder_path, ext)))
    
    image_files = sorted(image_files)
    
    print(f"📊 找到 {len(image_files)} 张图片")
    
    # TODO: 请在这里填入从微信公众号后台获取的URL规律
    # 示例: 如果您上传一张图片后获得URL为:
    # https://mmbiz.qpic.cn/mmbiz_jpg/ABC123DEF456/xyz789/0?wx_fmt=jpeg
    # 那么 base_url 应该设置为: "https://mmbiz.qpic.cn/mmbiz_jpg/ABC123DEF456"
    
    base_url = "https://mmbiz.qpic.cn/mmbiz_jpg/YOUR_ACCOUNT_ID_HERE"
    
    print(f"⚠️  请先修改 base_url 变量!")
    print(f"当前设置: {base_url}")
    
    if "YOUR_ACCOUNT_ID_HERE" in base_url:
        print(f"\n📝 操作步骤:")
        print(f"1. 手动上传1张图片到微信公众号后台")
        print(f"2. 获取图片的完整URL")
        print(f"3. 修改此脚本中的 base_url 变量")
        print(f"4. 重新运行此脚本")
        return
    
    # 生成所有URL
    urls = []
    
    for image_path in image_files:
        filename = os.path.basename(image_path)
        
        # 处理文件名中的特殊字符
        safe_filename = filename.replace(' ', '%20').replace('&', '%26').replace('=', '%3D')
        
        # 根据文件扩展名生成URL
        if filename.lower().endswith(('.jpg', '.jpeg')):
            # 移除扩展名
            name_without_ext = safe_filename.replace('.jpg', '').replace('.jpeg', '').replace('.JPG', '').replace('.JPEG', '')
            url = f"{base_url}/{name_without_ext}/0?wx_fmt=jpeg"
        elif filename.lower().endswith('.png'):
            name_without_ext = safe_filename.replace('.png', '').replace('.PNG', '')
            url = f"{base_url.replace('mmbiz_jpg', 'mmbiz_png')}/{name_without_ext}/0?wx_fmt=png"
        elif filename.lower().endswith('.gif'):
            name_without_ext = safe_filename.replace('.gif', '').replace('.GIF', '')
            url = f"{base_url.replace('mmbiz_jpg', 'mmbiz_gif')}/{name_without_ext}/0?wx_fmt=gif"
        else:
            # 其他格式
            name_without_ext = os.path.splitext(safe_filename)[0]
            url = f"{base_url}/{name_without_ext}/0?wx_fmt=other"
        
        urls.append(url)
    
    # 保存到文件
    with open("image_urls.txt", "w", encoding="utf-8") as f:
        for url in urls:
            f.write(url + "\n")
    
    print(f"\n✅ 已生成 {len(urls)} 个图片URL")
    print(f"📄 保存到文件: image_urls.txt")
    
    # 显示前几个URL作为示例
    print(f"\n📋 前5个URL示例:")
    for i, url in enumerate(urls[:5], 1):
        print(f"{i}. {url}")
    
    if len(urls) > 5:
        print(f"... 还有 {len(urls) - 5} 个URL")

def show_image_list():
    """显示所有图片文件列表"""
    
    folder_path = "C:/Users/<USER>/Desktop/图片案例"
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
        image_files.extend(glob.glob(os.path.join(folder_path, ext)))
    
    image_files = sorted(image_files)
    
    print(f"📁 图片文件夹: {folder_path}")
    print(f"📊 图片总数: {len(image_files)}")
    print(f"\n📋 所有图片文件:")
    
    for i, image_path in enumerate(image_files, 1):
        filename = os.path.basename(image_path)
        file_size = os.path.getsize(image_path) / 1024  # KB
        print(f"{i:3d}. {filename} ({file_size:.1f} KB)")

def main():
    print("🚀 微信公众号图片URL快速生成器")
    print("=" * 50)
    
    print(f"选择操作:")
    print(f"1. 查看图片文件列表")
    print(f"2. 生成图片URL")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        show_image_list()
    elif choice == "2":
        generate_image_urls()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
