
def generate_urls_from_pattern():
    """根据URL规律批量生成所有图片URL"""
    
    # TODO: 请填入从微信后台获取的URL规律
    # 示例: https://mmbiz.qpic.cn/mmbiz_jpg/XXXXXX/{filename}/0?wx_fmt=jpeg
    base_url_pattern = "https://mmbiz.qpic.cn/mmbiz_jpg/YOUR_ACCOUNT_ID/{filename}/0?wx_fmt=jpeg"
    
    image_files = [
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "20250721115246_687db98e5549e_0.png",
        "20250721115746_687dbabac8b2e_0.png",
        "getimgdata_msgid=421946876%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.png",
        "屏幕截图%202025-06-25%20163730.png",
        "屏幕截图%202025-07-22%20231516.png",
        "屏幕截图%202025-07-30%20015618.png",
        "屏幕截图%202025-08-01%20230827.png",
        "屏幕截图%202025-08-01%20231335.png",
        "屏幕截图%202025-08-01%20231401.png",
        "截图_20250620_172814.png",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "getimgdata_msgid=*********%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.jpg",
        "20250721115246_687db98e5549e_0.png",
        "20250721115746_687dbabac8b2e_0.png",
        "getimgdata_msgid=421946876%26mode=large%26source=%26fileId=%26ow=*********%26token=**********%26lang=zh_CN.png",
        "屏幕截图%202025-06-25%20163730.png",
        "屏幕截图%202025-07-22%20231516.png",
        "屏幕截图%202025-07-30%20015618.png",
        "屏幕截图%202025-08-01%20230827.png",
        "屏幕截图%202025-08-01%20231335.png",
        "屏幕截图%202025-08-01%20231401.png",
        "截图_20250620_172814.png",
    ]
    
    urls = []
    for filename in image_files:
        # 根据文件扩展名确定格式
        if filename.lower().endswith(('.jpg', '.jpeg')):
            url = base_url_pattern.replace('mmbiz_jpg', 'mmbiz_jpg').replace('{filename}', filename.replace('.jpg', '').replace('.jpeg', ''))
        elif filename.lower().endswith('.png'):
            url = base_url_pattern.replace('mmbiz_jpg', 'mmbiz_png').replace('{filename}', filename.replace('.png', ''))
        elif filename.lower().endswith('.gif'):
            url = base_url_pattern.replace('mmbiz_jpg', 'mmbiz_gif').replace('{filename}', filename.replace('.gif', ''))
        else:
            url = base_url_pattern.replace('{filename}', filename)
        
        urls.append(url)
    
    return urls

def save_urls_to_file():
    """保存URL到文件"""
    urls = generate_urls_from_pattern()
    
    with open("image_urls.txt", "w", encoding="utf-8") as f:
        for url in urls:
            f.write(url + "\n")
    
    print(f"✅ 已生成 {len(urls)} 个图片URL")
    print(f"📄 保存到文件: image_urls.txt")

if __name__ == "__main__":
    save_urls_to_file()
