import os
import glob

def find_image_folder():
    """查找图片文件夹"""
    
    # 可能的图片文件夹路径
    possible_paths = [
        "C:/Users/<USER>/Desktop/tu",
        "C:/Users/<USER>/Desktop/图片案例",
        "E:/8/3/tu", 
        "E:/tu",
        "D:/tu"
    ]
    
    print("🔍 查找图片文件夹...")
    
    for path in possible_paths:
        if os.path.exists(path):
            # 统计图片数量
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
                image_files.extend(glob.glob(os.path.join(path, ext)))
            
            if image_files:
                print(f"✅ 找到图片文件夹: {path}")
                print(f"📊 图片数量: {len(image_files)}")
                return path, image_files
    
    # 如果没找到，让用户手动输入
    print("❌ 未找到预设的图片文件夹")
    print("\n请手动输入图片文件夹路径:")
    
    while True:
        user_path = input("图片文件夹路径: ").strip().strip('"')
        if os.path.exists(user_path):
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
                image_files.extend(glob.glob(os.path.join(user_path, ext)))
            
            if image_files:
                print(f"✅ 确认文件夹: {user_path}")
                print(f"📊 图片数量: {len(image_files)}")
                return user_path, image_files
            else:
                print("❌ 该文件夹中没有图片文件")
        else:
            print("❌ 路径不存在，请重新输入")

def generate_url_template(image_files):
    """生成URL模板文件"""
    
    print("\n📝 生成URL模板文件...")
    
    # 创建模板文件
    with open("image_url_template.txt", "w", encoding="utf-8") as f:
        f.write("# 微信公众号图片URL模板\n")
        f.write("# 请按以下步骤操作:\n")
        f.write("# 1. 手动上传1-2张图片到微信公众号后台\n")
        f.write("# 2. 获取图片的永久URL\n")
        f.write("# 3. 分析URL规律，替换下面的模板\n")
        f.write("# 4. 批量生成所有图片URL\n\n")
        f.write("# URL模板示例:\n")
        f.write("# https://mmbiz.qpic.cn/mmbiz_jpg/XXXXXX/{filename}/0?wx_fmt=jpeg\n\n")
        f.write("# 图片文件列表:\n")
        
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            f.write(f"# {i:4d}. {filename}\n")
    
    print(f"✅ 模板文件已生成: image_url_template.txt")

def create_batch_upload_script(folder_path):
    """创建批量上传脚本"""
    
    script_content = f'''import os
import requests
import json
import time
import glob

# 微信公众号配置
APPID = "wxf8c888bfec881d88"
APPSECRET = "30a34b7f0cb3ab92ac2788d4fa17e559"

# 图片文件夹路径
FOLDER_PATH = r"{folder_path}"

def get_access_token():
    """获取微信access_token"""
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {{
        "grant_type": "client_credential",
        "appid": APPID,
        "secret": APPSECRET
    }}
    
    try:
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if "access_token" in data:
            print(f"✅ 获取access_token成功")
            return data["access_token"]
        else:
            print(f"❌ 获取access_token失败: {{data}}")
            return None
            
    except Exception as e:
        print(f"❌ 获取access_token异常: {{e}}")
        return None

def upload_image(image_path, access_token):
    """上传单张图片"""
    url = f"https://api.weixin.qq.com/cgi-bin/material/add_material"
    params = {{
        "access_token": access_token,
        "type": "image"
    }}
    
    try:
        with open(image_path, 'rb') as f:
            files = {{"media": f}}
            response = requests.post(url, params=params, files=files, timeout=60)
            data = response.json()
            
            if "url" in data:
                return data["url"]
            else:
                print(f"❌ 上传失败: {{os.path.basename(image_path)}} -> {{data}}")
                return None
                
    except Exception as e:
        print(f"❌ 上传异常: {{os.path.basename(image_path)}} -> {{e}}")
        return None

def main():
    # 获取所有图片文件
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.GIF', '*.BMP']:
        image_files.extend(glob.glob(os.path.join(FOLDER_PATH, ext)))
    
    print(f"📁 图片文件夹: {{FOLDER_PATH}}")
    print(f"📊 图片数量: {{len(image_files)}}")
    
    # 获取access_token
    access_token = get_access_token()
    if not access_token:
        print("❌ 无法获取access_token，请检查:")
        print("1. 网络连接")
        print("2. AppID和AppSecret是否正确")
        print("3. IP是否在微信白名单中")
        return
    
    # 开始批量上传
    urls = []
    success_count = 0
    
    print(f"\\n📤 开始批量上传...")
    
    for i, image_path in enumerate(image_files, 1):
        print(f"[{{i}}/{{len(image_files)}}] 上传: {{os.path.basename(image_path)}}")
        
        url = upload_image(image_path, access_token)
        if url:
            urls.append(url)
            success_count += 1
            print(f"✅ 成功: {{url}}")
        
        # 每次上传后等待1秒
        time.sleep(1)
        
        # 每10张显示进度
        if i % 10 == 0:
            print(f"📊 进度: {{i}}/{{len(image_files)}} ({{i/len(image_files)*100:.1f}}%)")
    
    # 保存URL到文件
    if urls:
        with open("image_urls.txt", "w", encoding="utf-8") as f:
            for url in urls:
                f.write(url + "\\n")
        
        print(f"\\n🎉 上传完成!")
        print(f"📊 成功上传: {{success_count}}/{{len(image_files)}} 张")
        print(f"📄 URL已保存到: image_urls.txt")
    else:
        print(f"\\n❌ 没有成功上传任何图片")

if __name__ == "__main__":
    main()
'''
    
    with open("batch_upload_final.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print(f"✅ 批量上传脚本已生成: batch_upload_final.py")

def main():
    print("🚀 微信公众号图片批量上传工具")
    print("=" * 50)
    
    # 查找图片文件夹
    folder_path, image_files = find_image_folder()
    
    if not image_files:
        print("❌ 未找到图片文件")
        return
    
    print(f"\\n📁 图片文件夹: {folder_path}")
    print(f"📊 图片数量: {len(image_files)}")
    
    # 生成URL模板
    generate_url_template(image_files)
    
    # 创建批量上传脚本
    create_batch_upload_script(folder_path)
    
    print(f"\\n📝 使用说明:")
    print(f"1. 如果微信API可用，直接运行: python batch_upload_final.py")
    print(f"2. 如果API不可用，参考 image_url_template.txt 手动处理")
    print(f"3. 所有URL将保存到 image_urls.txt 文件中")

if __name__ == "__main__":
    main()
