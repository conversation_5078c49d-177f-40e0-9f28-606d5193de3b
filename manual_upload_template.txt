# 微信公众号图片手动上传模板

## 📊 基本信息
- 图片总数: 34 张
- 文件夹: C:/Users/<USER>/Desktop/图片案例

## 🔧 操作步骤

### 1. 手动上传测试图片
请先上传以下3张图片到微信公众号后台:
1. 20250721115246_687db98e5549e_0.png
2. 20250721115246_687db98e5549e_0.png
3. 20250721115746_687dbabac8b2e_0.png

### 2. 获取URL规律
上传后获取图片URL，通常格式为:
https://mmbiz.qpic.cn/mmbiz_jpg/[账号ID]/[图片ID]/0?wx_fmt=jpeg

### 3. 批量生成URL
将获取的URL规律填入下面的Python代码中:

```python
# URL模板 - 请替换为实际获取的URL规律
base_url = "https://mmbiz.qpic.cn/mmbiz_jpg/YOUR_ACCOUNT_ID"

# 所有图片文件名
image_files = [
    "20250721115246_687db98e5549e_0.png",
    "20250721115246_687db98e5549e_0.png",
    "20250721115746_687dbabac8b2e_0.png",
    "20250721115746_687dbabac8b2e_0.png",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=421946873&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=421946873&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=421946874&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=421946874&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=421946875&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=421946875&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg",
    "getimgdata_msgid=421946876&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.png",
    "getimgdata_msgid=421946876&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.png",
    "屏幕截图 2025-06-25 163730.png",
    "屏幕截图 2025-06-25 163730.png",
    "屏幕截图 2025-07-22 231516.png",
    "屏幕截图 2025-07-22 231516.png",
    "屏幕截图 2025-07-30 015618.png",
    "屏幕截图 2025-07-30 015618.png",
    "屏幕截图 2025-08-01 230827.png",
    "屏幕截图 2025-08-01 230827.png",
    "屏幕截图 2025-08-01 231335.png",
    "屏幕截图 2025-08-01 231335.png",
    "屏幕截图 2025-08-01 231401.png",
    "屏幕截图 2025-08-01 231401.png",
    "截图_20250620_172814.png",
    "截图_20250620_172814.png",
]

# 生成所有URL
urls = []
for filename in image_files:
    # 根据文件扩展名调整URL
    if filename.lower().endswith(('.jpg', '.jpeg')):
        url = f"{base_url}/{filename.replace('.jpg', '').replace('.jpeg', '')}/0?wx_fmt=jpeg"
    elif filename.lower().endswith('.png'):
        url = f"{base_url.replace('mmbiz_jpg', 'mmbiz_png')}/{filename.replace('.png', '')}/0?wx_fmt=png"
    else:
        url = f"{base_url}/{filename}/0?wx_fmt=other"
    
    urls.append(url)

# 保存到文件
with open("image_urls.txt", "w", encoding="utf-8") as f:
    for url in urls:
        f.write(url + "\n")

print(f"已生成 {len(urls)} 个URL")
```

## 📝 完整图片列表
  1. 20250721115246_687db98e5549e_0.png (3074.1 KB)
  2. 20250721115246_687db98e5549e_0.png (3074.1 KB)
  3. 20250721115746_687dbabac8b2e_0.png (3074.1 KB)
  4. 20250721115746_687dbabac8b2e_0.png (3074.1 KB)
  5. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (117.7 KB)
  6. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (117.7 KB)
  7. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (201.2 KB)
  8. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (201.2 KB)
  9. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (95.8 KB)
 10. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (95.8 KB)
 11. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (129.0 KB)
 12. getimgdata_msgid=*********&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (129.0 KB)
 13. getimgdata_msgid=421946873&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (81.8 KB)
 14. getimgdata_msgid=421946873&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (81.8 KB)
 15. getimgdata_msgid=421946874&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (127.5 KB)
 16. getimgdata_msgid=421946874&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (127.5 KB)
 17. getimgdata_msgid=421946875&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (107.0 KB)
 18. getimgdata_msgid=421946875&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.jpg (107.0 KB)
 19. getimgdata_msgid=421946876&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.png (2666.4 KB)
 20. getimgdata_msgid=421946876&mode=large&source=&fileId=&ow=*********&token=**********&lang=zh_CN.png (2666.4 KB)
 21. 屏幕截图 2025-06-25 163730.png (501.1 KB)
 22. 屏幕截图 2025-06-25 163730.png (501.1 KB)
 23. 屏幕截图 2025-07-22 231516.png (72.2 KB)
 24. 屏幕截图 2025-07-22 231516.png (72.2 KB)
 25. 屏幕截图 2025-07-30 015618.png (367.2 KB)
 26. 屏幕截图 2025-07-30 015618.png (367.2 KB)
 27. 屏幕截图 2025-08-01 230827.png (492.2 KB)
 28. 屏幕截图 2025-08-01 230827.png (492.2 KB)
 29. 屏幕截图 2025-08-01 231335.png (297.2 KB)
 30. 屏幕截图 2025-08-01 231335.png (297.2 KB)
 31. 屏幕截图 2025-08-01 231401.png (225.1 KB)
 32. 屏幕截图 2025-08-01 231401.png (225.1 KB)
 33. 截图_20250620_172814.png (235.1 KB)
 34. 截图_20250620_172814.png (235.1 KB)
